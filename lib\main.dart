import 'package:flutter/material.dart';
import 'package:yaru/yaru.dart';
import 'screens/home_screen.dart';
import 'utils/theme_helper.dart';

Future<void> main() async {
  await YaruWindowTitleBar.ensureInitialized();

  WidgetsFlutterBinding.ensureInitialized();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {

    return YaruTheme(
      builder: (context, yaru, child) {
        return MaterialApp(
          title: '我能吞下玻璃而不伤身体',
          debugShowCheckedModeBanner: false,
          theme: ThemeHelper.overrideFontFamily(
            yaru.theme ?? ThemeData(),
            'HYWenHei 85W', // 使用现有的字体
          ),
          
          home: const HomeScreen(),
        );
      },
    );
  }
}
