import 'package:flutter/material.dart';

class AppTextStyles {
  // 默认字体
  static const String defaultFontFamily = 'LXGW WenKai';
  
  // 基础文本样式
  static const TextStyle body = TextStyle(
    fontFamily: defaultFontFamily,
    fontSize: 16,
  );
  
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: defaultFontFamily,
    fontSize: 18,
  );
  
  static const TextStyle bodySmall = TextStyle(
    fontFamily: defaultFontFamily,
    fontSize: 14,
  );
  
  // 标题样式
  static const TextStyle headline1 = TextStyle(
    fontFamily: defaultFontFamily,
    fontSize: 32,
    fontWeight: FontWeight.bold,
  );
  
  static const TextStyle headline2 = TextStyle(
    fontFamily: defaultFontFamily,
    fontSize: 28,
    fontWeight: FontWeight.bold,
  );
  
  static const TextStyle headline3 = TextStyle(
    fontFamily: defaultFontFamily,
    fontSize: 16,
  );
  
  static const TextStyle headline4 = TextStyle(
    fontFamily: defaultFontFamily,
    fontSize: 20,
    fontWeight: FontWeight.bold,
  );
  
  // 按钮样式
  static const TextStyle button = TextStyle(
    fontFamily: "苹方-简",
    fontSize: 16,
    fontWeight: FontWeight.normal,
  );
  
  // 特殊字体样式
  static const TextStyle neoXiHei = TextStyle(
    fontFamily: 'LXGWNeoXiHei',
    fontSize: 16,
  );
  
  static const TextStyle fzYouSong = TextStyle(
    fontFamily: 'FZYouSJK',
    fontSize: 16,
  );
  
  static const TextStyle codeFont = TextStyle(
    fontFamily: 'NeoXiHei Code',
    fontSize: 14,
  );
  
  // 工具方法：创建带有默认字体的TextStyle
  static TextStyle withDefaultFont({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    TextDecoration? decoration,
  }) {
    return TextStyle(
      fontFamily: defaultFontFamily,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      height: height,
      decoration: decoration,
    );
  }
  
  // 工具方法：创建带有指定字体的TextStyle
  static TextStyle withFont(
    String fontFamily, {
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    TextDecoration? decoration,
  }) {
    return TextStyle(
      fontFamily: fontFamily,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      height: height,
      decoration: decoration,
    );
  }
}
