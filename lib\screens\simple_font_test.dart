import 'package:flutter/material.dart';

class SimpleFontTest extends StatelessWidget {
  const SimpleFontTest({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('简单字体测试'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: <PERSON><PERSON><PERSON>(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 使用一些特殊字符来更容易区分字体差异
            _buildFontSample('LXGW WenKai', 'LXGW WenKai'),
            const SizedBox(height: 16),
            _buildFontSample('LXGWNeoXiHei', 'LXGWNeoXiHei'),
            const SizedBox(height: 16),
            _buildFontSample('FZYouSJK', 'FZYouSJK'),
            const SizedBox(height: 16),
            _buildFontSample('NeoXiHei Code', 'NeoXiHei Code'),
            const SizedBox(height: 16),
            _buildFontSample('系统默认', null),
            const SizedBox(height: 32),
            
            const Text(
              '如果上面的字体看起来都一样，可能的原因：',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text('1. 字体文件损坏或格式不正确'),
            const Text('2. pubspec.yaml配置有误'),
            const Text('3. Flutter字体缓存问题'),
            const Text('4. 字体文件路径错误'),
            const Text('5. 字体family名称不匹配'),
            const SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: () {
                // 显示字体配置信息
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('字体配置信息'),
                    content: const SingleChildScrollView(
                      child: Text('''
pubspec.yaml中的字体配置：

fonts:
  - family: LXGW WenKai
    fonts:
      - asset: fonts/LXGWWenKai-Regular.ttf
        weight: 400
  - family: LXGWNeoXiHei  
    fonts:
      - asset: fonts/LXGWNeoXiHei.ttf
        weight: 400
  - family: FZYouSJK
    fonts:
      - asset: fonts/FZYouSJK_505L.TTF
        weight: 300
      - asset: fonts/FZYouSJK_506L.TTF
        weight: 400
  - family: NeoXiHei Code
    fonts:
      - asset: fonts/NeoXiHeiCode-Regular.ttf
        weight: 400

如果字体显示相同，请尝试：
1. flutter clean
2. flutter pub get
3. 重新运行应用
                      '''),
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('关闭'),
                      ),
                    ],
                  ),
                );
              },
              child: const Text('查看字体配置信息'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFontSample(String label, String? fontFamily) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label:',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '我能吞下玻璃而不伤身体 The quick brown fox jumps over the lazy dog 1234567890',
            style: TextStyle(
              fontSize: 18,
              fontFamily: fontFamily,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '特殊字符: ≠ ≤ ≥ ∞ ∑ ∏ ∫ ∂ ∆ ∇ ∈ ∉ ∪ ∩ ⊂ ⊃ ← → ↑ ↓ ↔ ↕',
            style: TextStyle(
              fontSize: 16,
              fontFamily: fontFamily,
            ),
          ),
        ],
      ),
    );
  }
}
