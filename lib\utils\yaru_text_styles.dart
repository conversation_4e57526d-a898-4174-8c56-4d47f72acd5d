import 'package:flutter/material.dart';
import 'package:yaru/yaru.dart';

/// Yaru主题下的文本样式工具类
/// 提供类似Fluent主题的typography访问方式
class YaruTextStyles {
  /// 获取Yaru主题的文本样式
  static YaruThemeData? _getYaruTheme(BuildContext context) {
    return YaruTheme.of(context);
  }

  /// 获取Material主题数据
  static ThemeData _getThemeData(BuildContext context) {
    return Theme.of(context);
  }

  /// 获取bodyLarge样式，类似Fluent主题的typography.bodyLarge
  static TextStyle? bodyLarge(BuildContext context, {
    double? fontSize,
    String? fontFamily,
    Color? color,
    FontWeight? fontWeight,
  }) {
    final theme = _getThemeData(context);
    return theme.textTheme.bodyLarge?.copyWith(
      fontSize: fontSize,
      fontFamily: fontFamily ?? 'Microsoft YaHei UI',
      color: color,
      fontWeight: fontWeight,
    );
  }

  /// 获取bodyMedium样式
  static TextStyle? bodyMedium(BuildContext context, {
    double? fontSize,
    String? fontFamily,
    Color? color,
    FontWeight? fontWeight,
  }) {
    final theme = _getThemeData(context);
    return theme.textTheme.bodyMedium?.copyWith(
      fontSize: fontSize,
      fontFamily: fontFamily ?? 'Microsoft YaHei UI',
      color: color,
      fontWeight: fontWeight,
    );
  }

  /// 获取bodySmall样式
  static TextStyle? bodySmall(BuildContext context, {
    double? fontSize,
    String? fontFamily,
    Color? color,
    FontWeight? fontWeight,
  }) {
    final theme = _getThemeData(context);
    return theme.textTheme.bodySmall?.copyWith(
      fontSize: fontSize,
      fontFamily: fontFamily ?? 'Microsoft YaHei UI',
      color: color,
      fontWeight: fontWeight,
    );
  }

  /// 获取displayLarge样式
  static TextStyle? displayLarge(BuildContext context, {
    double? fontSize,
    String? fontFamily,
    Color? color,
    FontWeight? fontWeight,
  }) {
    final theme = _getThemeData(context);
    return theme.textTheme.displayLarge?.copyWith(
      fontSize: fontSize,
      fontFamily: fontFamily ?? 'Microsoft YaHei UI',
      color: color,
      fontWeight: fontWeight,
    );
  }

  /// 获取displayMedium样式
  static TextStyle? displayMedium(BuildContext context, {
    double? fontSize,
    String? fontFamily,
    Color? color,
    FontWeight? fontWeight,
  }) {
    final theme = _getThemeData(context);
    return theme.textTheme.displayMedium?.copyWith(
      fontSize: fontSize,
      fontFamily: fontFamily ?? 'Microsoft YaHei UI',
      color: color,
      fontWeight: fontWeight,
    );
  }

  /// 获取displaySmall样式
  static TextStyle? displaySmall(BuildContext context, {
    double? fontSize,
    String? fontFamily,
    Color? color,
    FontWeight? fontWeight,
  }) {
    final theme = _getThemeData(context);
    return theme.textTheme.displaySmall?.copyWith(
      fontSize: fontSize,
      fontFamily: fontFamily ?? 'Microsoft YaHei UI',
      color: color,
      fontWeight: fontWeight,
    );
  }

  /// 获取headlineLarge样式
  static TextStyle? headlineLarge(BuildContext context, {
    double? fontSize,
    String? fontFamily,
    Color? color,
    FontWeight? fontWeight,
  }) {
    final theme = _getThemeData(context);
    return theme.textTheme.headlineLarge?.copyWith(
      fontSize: fontSize,
      fontFamily: fontFamily ?? 'Microsoft YaHei UI',
      color: color,
      fontWeight: fontWeight,
    );
  }

  /// 获取headlineMedium样式
  static TextStyle? headlineMedium(BuildContext context, {
    double? fontSize,
    String? fontFamily,
    Color? color,
    FontWeight? fontWeight,
  }) {
    final theme = _getThemeData(context);
    return theme.textTheme.headlineMedium?.copyWith(
      fontSize: fontSize,
      fontFamily: fontFamily ?? 'Microsoft YaHei UI',
      color: color,
      fontWeight: fontWeight,
    );
  }

  /// 获取headlineSmall样式
  static TextStyle? headlineSmall(BuildContext context, {
    double? fontSize,
    String? fontFamily,
    Color? color,
    FontWeight? fontWeight,
  }) {
    final theme = _getThemeData(context);
    return theme.textTheme.headlineSmall?.copyWith(
      fontSize: fontSize,
      fontFamily: fontFamily ?? 'Microsoft YaHei UI',
      color: color,
      fontWeight: fontWeight,
    );
  }

  /// 获取titleLarge样式
  static TextStyle? titleLarge(BuildContext context, {
    double? fontSize,
    String? fontFamily,
    Color? color,
    FontWeight? fontWeight,
  }) {
    final theme = _getThemeData(context);
    return theme.textTheme.titleLarge?.copyWith(
      fontSize: fontSize,
      fontFamily: fontFamily ?? 'Microsoft YaHei UI',
      color: color,
      fontWeight: fontWeight,
    );
  }

  /// 获取titleMedium样式
  static TextStyle? titleMedium(BuildContext context, {
    double? fontSize,
    String? fontFamily,
    Color? color,
    FontWeight? fontWeight,
  }) {
    final theme = _getThemeData(context);
    return theme.textTheme.titleMedium?.copyWith(
      fontSize: fontSize,
      fontFamily: fontFamily ?? 'Microsoft YaHei UI',
      color: color,
      fontWeight: fontWeight,
    );
  }

  /// 获取titleSmall样式
  static TextStyle? titleSmall(BuildContext context, {
    double? fontSize,
    String? fontFamily,
    Color? color,
    FontWeight? fontWeight,
  }) {
    final theme = _getThemeData(context);
    return theme.textTheme.titleSmall?.copyWith(
      fontSize: fontSize,
      fontFamily: fontFamily ?? 'Microsoft YaHei UI',
      color: color,
      fontWeight: fontWeight,
    );
  }

  /// 获取labelLarge样式
  static TextStyle? labelLarge(BuildContext context, {
    double? fontSize,
    String? fontFamily,
    Color? color,
    FontWeight? fontWeight,
  }) {
    final theme = _getThemeData(context);
    return theme.textTheme.labelLarge?.copyWith(
      fontSize: fontSize,
      fontFamily: fontFamily ?? 'Microsoft YaHei UI',
      color: color,
      fontWeight: fontWeight,
    );
  }

  /// 获取labelMedium样式
  static TextStyle? labelMedium(BuildContext context, {
    double? fontSize,
    String? fontFamily,
    Color? color,
    FontWeight? fontWeight,
  }) {
    final theme = _getThemeData(context);
    return theme.textTheme.labelMedium?.copyWith(
      fontSize: fontSize,
      fontFamily: fontFamily ?? 'Microsoft YaHei UI',
      color: color,
      fontWeight: fontWeight,
    );
  }

  /// 获取labelSmall样式
  static TextStyle? labelSmall(BuildContext context, {
    double? fontSize,
    String? fontFamily,
    Color? color,
    FontWeight? fontWeight,
  }) {
    final theme = _getThemeData(context);
    return theme.textTheme.labelSmall?.copyWith(
      fontSize: fontSize,
      fontFamily: fontFamily ?? 'Microsoft YaHei UI',
      color: color,
      fontWeight: fontWeight,
    );
  }

  /// 创建自定义文本样式
  static TextStyle customStyle(BuildContext context, {
    required TextStyle baseStyle,
    double? fontSize,
    String? fontFamily,
    Color? color,
    FontWeight? fontWeight,
  }) {
    return baseStyle.copyWith(
      fontSize: fontSize,
      fontFamily: fontFamily ?? 'Microsoft YaHei UI',
      color: color,
      fontWeight: fontWeight,
    );
  }
}
