import 'package:flutter/material.dart';
import 'package:yaru/yaru.dart';
import 'font_test_screen.dart';
import 'simple_font_test.dart';
import 'font_debug_screen.dart';


class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color.fromARGB(255, 252,252,249),
      appBar: YaruWindowTitleBar(
        title: Text(
            '少女身体前倾，两手扶着面前圆桌的边缘我能吞下玻璃而不伤身体。这是一段测试文本，包含中文、English、数字123和符号！@#',
          ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),
            Text('测试默认字体 - 完全没有style'),
            Text('测试指定颜色', style: TextStyle(color: Colors.amber)),
            const SizedBox(height: 16),
            Text(
                """需要少女身体前倾，两手扶着面前圆桌的边缘，腰肢向下弯曲，将雪嫩的圆臀翘在程宗扬小腹上""",
                style: TextStyle(
                  fontSize: 16,
                  color: Color.fromARGB(255, 18, 51, 58),
                ),
              ),
            const SizedBox(height: 24),
            OutlinedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const FontTestScreen(),
                  ),
                );
              },
              child: Text(
                '少女，打开字体测试页面，们需要测试字体',
              ),
            ),
            const SizedBox(height: 16),
            FilledButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SimpleFontTest(),
                  ),
                );
              },
              child: Text(
                '少女需要简单字体测试',
                style: TextStyle(
                  fontSize: 21,
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const FontDebugScreen(),
                  ),
                );
              },
              child: Text(
                '字体调试页面',
              ),
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}
