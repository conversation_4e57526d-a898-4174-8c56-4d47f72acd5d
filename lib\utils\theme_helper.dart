import 'package:flutter/material.dart';

class ThemeHelper {
  static const String defaultFontFamily = 'LXGW Wen<PERSON>ai';
  
  /// 强制覆盖主题中的所有字体
  static ThemeData overrideFontFamily(ThemeData baseTheme, String fontFamily) {
    return baseTheme.copyWith(
      // 覆盖所有文本主题 - 这是Text控件的主要来源
      textTheme: _overrideTextTheme(baseTheme.textTheme, fontFamily),
      primaryTextTheme: _overrideTextTheme(baseTheme.primaryTextTheme, fontFamily),
      
      // 覆盖特定组件的字体
      appBarTheme: baseTheme.appBarTheme.copyWith(
        backgroundColor: Color.fromARGB(255, 111,252,249),
        titleTextStyle: baseTheme.appBarTheme.titleTextStyle?.copyWith(
          fontFamily: "苹方-简",
          fontSize: 5,
        ),
        toolbarTextStyle: baseTheme.appBarTheme.toolbarTextStyle?.copyWith(
          fontFamily: fontFamily,
        ),
      ),
      
      tabBarTheme: baseTheme.tabBarTheme.copyWith(
        labelStyle: baseTheme.tabBarTheme.labelStyle?.copyWith(
          fontFamily: fontFamily,
        ),
        unselectedLabelStyle: baseTheme.tabBarTheme.unselectedLabelStyle?.copyWith(
          fontFamily: fontFamily,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: baseTheme.elevatedButtonTheme.style?.copyWith(
          textStyle: WidgetStateProperty.all(
            baseTheme.elevatedButtonTheme.style?.textStyle?.resolve({})?.copyWith(
              fontFamily: fontFamily,
            ) ?? TextStyle(fontFamily: fontFamily),
          ),
        ),
      ),
      filledButtonTheme: FilledButtonThemeData(
        style: baseTheme.elevatedButtonTheme.style?.copyWith(
          textStyle: WidgetStateProperty.all(
            baseTheme.elevatedButtonTheme.style?.textStyle?.resolve({})?.copyWith(
              fontFamily: fontFamily,
            ) ?? TextStyle(fontFamily: fontFamily),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: baseTheme.textButtonTheme.style?.copyWith(
          textStyle: WidgetStateProperty.all(
            baseTheme.textButtonTheme.style?.textStyle?.resolve({})?.copyWith(
              fontFamily: fontFamily,
            ) ?? TextStyle(fontFamily: fontFamily),
          ),
        ),
      ),
      
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: baseTheme.outlinedButtonTheme.style?.copyWith(
          textStyle: WidgetStateProperty.all(
            baseTheme.outlinedButtonTheme.style?.textStyle?.resolve({})?.copyWith(
              fontFamily: fontFamily,
            ) ?? TextStyle(fontFamily: fontFamily),
          ),
        ),
      ),
      
      inputDecorationTheme: baseTheme.inputDecorationTheme.copyWith(
        labelStyle: baseTheme.inputDecorationTheme.labelStyle?.copyWith(
          fontFamily: fontFamily,
        ),
        hintStyle: baseTheme.inputDecorationTheme.hintStyle?.copyWith(
          fontFamily: fontFamily,
        ),
        helperStyle: baseTheme.inputDecorationTheme.helperStyle?.copyWith(
          fontFamily: fontFamily,
        ),
        errorStyle: baseTheme.inputDecorationTheme.errorStyle?.copyWith(
          fontFamily: fontFamily,
        ),
      ),
      
     
    );
  }
  
  /// 覆盖TextTheme中的所有字体
  static TextTheme _overrideTextTheme(TextTheme textTheme, String fontFamily) {
    // 创建一个基础样式，确保所有Text都有正确的字体
    const baseStyle = TextStyle();
    
    return textTheme.copyWith(
      displayLarge: textTheme.displayLarge?.copyWith(fontFamily: fontFamily),
      displayMedium: textTheme.displayMedium?.copyWith(fontFamily: fontFamily),
      displaySmall: textTheme.displaySmall?.copyWith(fontFamily: fontFamily),
      headlineLarge: textTheme.headlineLarge?.copyWith(fontFamily: fontFamily),
      headlineMedium: textTheme.headlineMedium?.copyWith(fontFamily: fontFamily),
      headlineSmall: textTheme.headlineSmall?.copyWith(fontFamily: fontFamily),
      titleLarge: textTheme.titleLarge?.copyWith(fontFamily: fontFamily),
      titleMedium: textTheme.titleMedium?.copyWith(fontFamily: fontFamily),
      titleSmall: textTheme.titleSmall?.copyWith(fontFamily: fontFamily),
      bodyLarge: textTheme.bodyLarge?.copyWith(fontFamily: fontFamily),
      bodySmall: textTheme.bodySmall?.copyWith(fontFamily: fontFamily),
      labelLarge: textTheme.labelLarge?.copyWith(fontFamily: fontFamily),
      labelMedium: textTheme.labelMedium?.copyWith(fontFamily: fontFamily),
      labelSmall: textTheme.labelSmall?.copyWith(fontFamily: fontFamily),
      
      // Text控件的默认样式 - 这是关键！
      bodyMedium: baseStyle.copyWith(
        fontFamily: fontFamily,
        fontSize: 16, // 确保有默认大小
      ),
    );
  }
  
  /// 创建一个完全使用指定字体的主题
  static ThemeData createThemeWithFont(
    String fontFamily, {
    ColorScheme? colorScheme,
    bool useMaterial3 = true,
    Brightness? brightness,
  }) {
    return ThemeData(
      fontFamily: fontFamily,
      colorScheme: colorScheme,
      useMaterial3: useMaterial3,
      brightness: brightness,
    );
  }
}
