import 'package:flutter/material.dart';

class FontDebugScreen extends StatelessWidget {
  const FontDebugScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('字体调试页面'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: <PERSON>um<PERSON>(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '字体调试测试',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            
            // 测试1: 使用明显不同的字符来区分字体
            _buildTestSection('测试1: 使用特殊字符区分字体'),
            _buildFontTest('LXGWWenKai', 'LXGWWenKai', '霞鹜文楷 ≠ ≤ ≥ ∞ ∑ ∏'),
            _buildFontTest('LXGWNeoXiHei', 'LXGWNeoXiHei', '霞鹜新晰黑 ≠ ≤ ≥ ∞ ∑ ∏'),
            _buildFontTest('FZYouSJK', 'FZYouSJK', '方正悠宋 ≠ ≤ ≥ ∞ ∑ ∏'),
            _buildFontTest('NeoXiHeiCode', 'NeoXiHeiCode', '新晰黑代码 ≠ ≤ ≥ ∞ ∑ ∏'),
            _buildFontTest('系统默认', null, '系统默认字体 ≠ ≤ ≥ ∞ ∑ ∏'),
            
            const SizedBox(height: 30),
            
            // 测试2: 使用不存在的字体名称来验证回退机制
            _buildTestSection('测试2: 不存在的字体（应该回退到系统字体）'),
            _buildFontTest('不存在的字体', 'NonExistentFont', '这应该显示系统默认字体'),
            
            const SizedBox(height: 30),
            
            // 测试3: 字体加载状态检查
            _buildTestSection('测试3: 字体配置检查'),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('当前字体配置:'),
                  Text('• LXGW WenKai -> fonts/LXGWWenKai-Regular.ttf'),
                  Text('• LXGWNeoXiHei -> fonts/LXGWNeoXiHei.ttf'),
                  Text('• FZYouSJK -> fonts/FZYouSJK_505L.TTF, fonts/FZYouSJK_506L.TTF'),
                  Text('• NeoXiHei Code -> fonts/NeoXiHeiCode-Regular.ttf'),
                  SizedBox(height: 8),
                  Text('如果所有字体看起来相同，可能的问题:'),
                  Text('1. 字体文件损坏'),
                  Text('2. 字体内部名称与family名称不匹配'),
                  Text('3. Flutter字体缓存问题'),
                  Text('4. 字体文件格式不支持'),
                ],
              ),
            ),
            
            const SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: () {
                _showFontInfo(context);
              },
              child: const Text('显示详细字体信息'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestSection(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.blue,
        ),
      ),
    );
  }

  Widget _buildFontTest(String label, String? fontFamily, String text) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label (fontFamily: ${fontFamily ?? "null"}):',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 20,
              fontFamily: fontFamily,
            ),
          ),
        ],
      ),
    );
  }

  void _showFontInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('字体调试信息'),
        content: const SingleChildScrollView(
          child: Text('''
字体调试步骤:

1. 检查字体文件是否存在:
   - fonts/LXGWWenKai-Regular.ttf
   - fonts/LXGWNeoXiHei.ttf
   - fonts/FZYouSJK_505L.TTF
   - fonts/FZYouSJK_506L.TTF
   - fonts/NeoXiHeiCode-Regular.ttf

2. 检查pubspec.yaml配置是否正确

3. 运行以下命令清理缓存:
   flutter clean
   flutter pub get

4. 如果问题仍然存在，可能是:
   - 字体文件内部名称与配置不匹配
   - 字体文件格式问题
   - Flutter版本兼容性问题

5. 尝试使用字体编辑器查看字体文件的内部名称
          '''),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
